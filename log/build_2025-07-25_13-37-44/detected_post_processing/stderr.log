In file included from [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:21[m[K:
[01m[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:[m[K [01;35m[Kwarning: [m[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp-Wcpp]8;;[m[K]
   35 | #[01;35m[Kwarning[m[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead
      |  [01;35m[K^~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kvoid find_three_points(const std::vector<cv::Vec<float, 3> >&, std::vector<cv::Point_<float> >&)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:64:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
   64 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
      |         [01;35m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kstd::vector<cv::Point_<float> > image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:127:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  127 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
      |         [01;35m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kvoid calculatePixelTo3DScale(const cv::Mat&, const cv::Mat&, const cv::Mat&)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:300:45:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Krvec[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  300 |                              [01;35m[Kconst cv::Mat& rvec[m[K) {
      |                              [01;35m[K~~~~~~~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:708:14:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kargc[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  708 | int main([01;35m[Kint argc[m[K, char *argv[]){
      |          [01;35m[K~~~~^~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:708:26:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kargv[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  708 | int main(int argc, [01;35m[Kchar *argv[][m[K){
      |                    [01;35m[K~~~~~~^~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:[m[K In member function ‘[01m[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:317:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  317 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
      |         [01;35m[K^~~~~~~~~~~[m[K
