[0.011s] Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': AMENT_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
[0.102s] [ 50%] [32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o[0m
[0.102s] [ 50%] [32mBuilding CXX object CMakeFiles/test_post_processing.dir/src/test.cpp.o[0m
[0.322s] In file included from [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:21[m[K:
[0.322s] [01m[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:[m[K [01;35m[Kwarning: [m[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp-Wcpp]8;;[m[K]
[0.322s]    35 | #[01;35m[Kwarning[m[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead
[0.322s]       |  [01;35m[K^~~~~~~[m[K
[2.043s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kvoid find_three_points(const std::vector<cv::Vec<float, 3> >&, std::vector<cv::Point_<float> >&)[m[K’:
[2.044s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:64:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[2.044s]    64 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
[2.044s]       |         [01;35m[K^~~~~~~~~~~[m[K
[2.045s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kstd::vector<cv::Point_<float> > image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[2.045s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:127:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[2.045s]   127 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
[2.045s]       |         [01;35m[K^~~~~~~~~~~[m[K
[2.058s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kvoid calculatePixelTo3DScale(const cv::Mat&, const cv::Mat&, const cv::Mat&)[m[K’:
[2.058s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:300:45:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Krvec[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[2.058s]   300 |                              [01;35m[Kconst cv::Mat& rvec[m[K) {
[2.058s]       |                              [01;35m[K~~~~~~~~~~~~~~~^~~~[m[K
[2.076s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[2.076s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:708:14:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kargc[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[2.076s]   708 | int main([01;35m[Kint argc[m[K, char *argv[]){
[2.076s]       |          [01;35m[K~~~~^~~~[m[K
[2.076s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:708:26:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kargv[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[2.076s]   708 | int main(int argc, [01;35m[Kchar *argv[][m[K){
[2.077s]       |                    [01;35m[K~~~~~~^~~~~~[m[K
[2.780s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:[m[K In member function ‘[01m[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[2.780s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:317:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[2.780s]   317 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
[2.781s]       |         [01;35m[K^~~~~~~~~~~[m[K
[3.179s] [ 75%] [32m[1mLinking CXX executable test_post_processing[0m
[3.662s] [ 75%] Built target test_post_processing
[10.543s] [100%] [32m[1mLinking CXX executable post_processing[0m
[11.344s] [100%] Built target post_processing
[11.355s] Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
[11.361s] Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': AMENT_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/bin/cmake --install /home/<USER>/ccag/ccag_ws/build/detected_post_processing
[11.367s] -- Install configuration: ""
[11.368s] -- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing
[11.379s] -- Set non-toolchain portion of runtime path of "/home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing" to ""
[11.379s] -- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/test_post_processing
[11.379s] -- Set non-toolchain portion of runtime path of "/home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/test_post_processing" to ""
[11.379s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config
[11.379s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config/camera_info.yaml
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/package_run_dependencies/detected_post_processing
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/parent_prefix_path/detected_post_processing
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.sh
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.dsv
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.sh
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.dsv
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.bash
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.sh
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.zsh
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.dsv
[11.380s] -- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.dsv
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/packages/detected_post_processing
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig.cmake
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig-version.cmake
[11.380s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.xml
[11.382s] Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/bin/cmake --install /home/<USER>/ccag/ccag_ws/build/detected_post_processing
