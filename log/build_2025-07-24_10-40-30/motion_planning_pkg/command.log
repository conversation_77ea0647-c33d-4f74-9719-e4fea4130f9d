Invoking command in '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg': PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/motion_planning_pkg build --build-base /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build install --record /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg' returned '0': PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/motion_planning_pkg build --build-base /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build install --record /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log --single-version-externally-managed install_data
