[0.069s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'motion_planning_pkg']
[0.069s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=28, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['motion_planning_pkg'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7e7e702168c0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7e7e7034fca0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7e7e7034fca0>>)
[0.094s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.094s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.094s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.094s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.094s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.094s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.094s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ccag/ccag_ws'
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.095s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.095s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extensions ['ignore', 'ignore_ament_install']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'ignore'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'ignore_ament_install'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extensions ['colcon_pkg']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'colcon_pkg'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extensions ['colcon_meta']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'colcon_meta'
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extensions ['ros']
[0.105s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'ros'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extensions ['cmake', 'python']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'cmake'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'python'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extensions ['python_setup_py']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(model) by extension 'python_setup_py'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extensions ['ignore', 'ignore_ament_install']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extension 'ignore'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extension 'ignore_ament_install'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extensions ['colcon_pkg']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extension 'colcon_pkg'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extensions ['colcon_meta']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extension 'colcon_meta'
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extensions ['ros']
[0.106s] Level 1:colcon.colcon_core.package_identification:_identify(src/data_interface) by extension 'ros'
[0.108s] DEBUG:colcon.colcon_core.package_identification:Package 'src/data_interface' with type 'ros.ament_cmake' and name 'data_interface'
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extensions ['ignore', 'ignore_ament_install']
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extension 'ignore'
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extension 'ignore_ament_install'
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extensions ['colcon_pkg']
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extension 'colcon_pkg'
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extensions ['colcon_meta']
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extension 'colcon_meta'
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extensions ['ros']
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(src/detected_post_processing) by extension 'ros'
[0.109s] DEBUG:colcon.colcon_core.package_identification:Package 'src/detected_post_processing' with type 'ros.ament_cmake' and name 'detected_post_processing'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'ignore'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'ignore_ament_install'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extensions ['colcon_pkg']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'colcon_pkg'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extensions ['colcon_meta']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'colcon_meta'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extensions ['ros']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'ros'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extensions ['cmake', 'python']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'cmake'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'python'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extensions ['python_setup_py']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2) by extension 'python_setup_py'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extensions ['ignore', 'ignore_ament_install']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extension 'ignore'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extension 'ignore_ament_install'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extensions ['colcon_pkg']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extension 'colcon_pkg'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extensions ['colcon_meta']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extension 'colcon_meta'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extensions ['ros']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/camera_calibration) by extension 'ros'
[0.110s] DEBUG:colcon.colcon_core.package_identification:Package 'src/handeye_calibration_ros2/camera_calibration' with type 'ros.ament_python' and name 'camera_calibration'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extensions ['ignore', 'ignore_ament_install']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'ignore'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'ignore_ament_install'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extensions ['colcon_pkg']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'colcon_pkg'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extensions ['colcon_meta']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'colcon_meta'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extensions ['ros']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'ros'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extensions ['cmake', 'python']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'cmake'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'python'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extensions ['python_setup_py']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/doc) by extension 'python_setup_py'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extensions ['ignore', 'ignore_ament_install']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extension 'ignore'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extension 'ignore_ament_install'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extensions ['colcon_pkg']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extension 'colcon_pkg'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extensions ['colcon_meta']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extension 'colcon_meta'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extensions ['ros']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/handeye_calibration_ros2/handeye_realsense) by extension 'ros'
[0.111s] DEBUG:colcon.colcon_core.package_identification:Package 'src/handeye_calibration_ros2/handeye_realsense' with type 'ros.ament_python' and name 'handeye_realsense'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extension 'ignore'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extension 'ignore_ament_install'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extensions ['colcon_pkg']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extension 'colcon_pkg'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extensions ['colcon_meta']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extension 'colcon_meta'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extensions ['ros']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/motion_planning_pkg) by extension 'ros'
[0.112s] DEBUG:colcon.colcon_core.package_identification:Package 'src/motion_planning_pkg' with type 'ros.ament_python' and name 'motion_planning_pkg'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extensions ['ignore', 'ignore_ament_install']
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extension 'ignore'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extension 'ignore_ament_install'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extensions ['colcon_pkg']
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extension 'colcon_pkg'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extensions ['colcon_meta']
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extension 'colcon_meta'
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extensions ['ros']
[0.112s] Level 1:colcon.colcon_core.package_identification:_identify(src/mujoco_sim) by extension 'ros'
[0.113s] DEBUG:colcon.colcon_core.package_identification:Package 'src/mujoco_sim' with type 'ros.ament_python' and name 'mujoco_sim'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extensions ['ignore', 'ignore_ament_install']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extension 'ignore'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extension 'ignore_ament_install'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extensions ['colcon_pkg']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extension 'colcon_pkg'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extensions ['colcon_meta']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extension 'colcon_meta'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extensions ['ros']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation) by extension 'ros'
[0.113s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pose_estimation' with type 'ros.ament_cmake' and name 'pose_estimation'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extensions ['ignore', 'ignore_ament_install']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extension 'ignore'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extension 'ignore_ament_install'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extensions ['colcon_pkg']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extension 'colcon_pkg'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extensions ['colcon_meta']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extension 'colcon_meta'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extensions ['ros']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/pose_estimation_client) by extension 'ros'
[0.114s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pose_estimation_client' with type 'ros.ament_cmake' and name 'pose_estimation_client'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extensions ['ignore', 'ignore_ament_install']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extension 'ignore'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extension 'ignore_ament_install'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extensions ['colcon_pkg']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extension 'colcon_pkg'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extensions ['colcon_meta']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extension 'colcon_meta'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extensions ['ros']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/qt_ros_test) by extension 'ros'
[0.115s] DEBUG:colcon.colcon_core.package_identification:Package 'src/qt_ros_test' with type 'ros.ament_cmake' and name 'qt_ros_test'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extensions ['ignore', 'ignore_ament_install']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'ignore'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'ignore_ament_install'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extensions ['colcon_pkg']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'colcon_pkg'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extensions ['colcon_meta']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'colcon_meta'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extensions ['ros']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'ros'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extensions ['cmake', 'python']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'cmake'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'python'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extensions ['python_setup_py']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros) by extension 'python_setup_py'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extensions ['ignore', 'ignore_ament_install']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'ignore'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'ignore_ament_install'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extensions ['colcon_pkg']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'colcon_pkg'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extensions ['colcon_meta']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'colcon_meta'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extensions ['ros']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'ros'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extensions ['cmake', 'python']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'cmake'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'python'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extensions ['python_setup_py']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/docs) by extension 'python_setup_py'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extensions ['ignore', 'ignore_ament_install']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extension 'ignore'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extension 'ignore_ament_install'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extensions ['colcon_pkg']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extension 'colcon_pkg'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extensions ['colcon_meta']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extension 'colcon_meta'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extensions ['ros']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_bringup) by extension 'ros'
[0.116s] DEBUG:colcon.colcon_core.package_identification:Package 'src/yolo_ros/yolo_bringup' with type 'ros.ament_cmake' and name 'yolo_bringup'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extension 'ignore'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extension 'ignore_ament_install'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extensions ['colcon_pkg']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extension 'colcon_pkg'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extensions ['colcon_meta']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extension 'colcon_meta'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extensions ['ros']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_msgs) by extension 'ros'
[0.117s] DEBUG:colcon.colcon_core.package_identification:Package 'src/yolo_ros/yolo_msgs' with type 'ros.ament_cmake' and name 'yolo_msgs'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extensions ['ignore', 'ignore_ament_install']
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extension 'ignore'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extension 'ignore_ament_install'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extensions ['colcon_pkg']
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extension 'colcon_pkg'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extensions ['colcon_meta']
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extension 'colcon_meta'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extensions ['ros']
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(src/yolo_ros/yolo_ros) by extension 'ros'
[0.117s] DEBUG:colcon.colcon_core.package_identification:Package 'src/yolo_ros/yolo_ros' with type 'ros.ament_python' and name 'yolo_ros'
[0.117s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.117s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.117s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.117s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.117s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'camera_calibration' in 'src/handeye_calibration_ros2/camera_calibration'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'data_interface' in 'src/data_interface'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'handeye_realsense' in 'src/handeye_calibration_ros2/handeye_realsense'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'pose_estimation_client' in 'src/pose_estimation_client'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'qt_ros_test' in 'src/qt_ros_test'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'yolo_msgs' in 'src/yolo_ros/yolo_msgs'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'detected_post_processing' in 'src/detected_post_processing'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'mujoco_sim' in 'src/mujoco_sim'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'pose_estimation' in 'src/pose_estimation'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'yolo_ros' in 'src/yolo_ros/yolo_ros'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'yolo_bringup' in 'src/yolo_ros/yolo_bringup'
[0.132s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.132s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.133s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 12 installed packages in /home/<USER>/ccag/ccag_ws/install
[0.134s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 277 installed packages in /opt/ros/humble
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.156s] Level 5:colcon.colcon_core.verb:set package 'motion_planning_pkg' build argument 'cmake_args' from command line to 'None'
[0.156s] Level 5:colcon.colcon_core.verb:set package 'motion_planning_pkg' build argument 'cmake_target' from command line to 'None'
[0.156s] Level 5:colcon.colcon_core.verb:set package 'motion_planning_pkg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.156s] Level 5:colcon.colcon_core.verb:set package 'motion_planning_pkg' build argument 'cmake_clean_cache' from command line to 'False'
[0.156s] Level 5:colcon.colcon_core.verb:set package 'motion_planning_pkg' build argument 'cmake_clean_first' from command line to 'False'
[0.156s] Level 5:colcon.colcon_core.verb:set package 'motion_planning_pkg' build argument 'cmake_force_configure' from command line to 'False'
[0.156s] Level 5:colcon.colcon_core.verb:set package 'motion_planning_pkg' build argument 'ament_cmake_args' from command line to 'None'
[0.156s] Level 5:colcon.colcon_core.verb:set package 'motion_planning_pkg' build argument 'catkin_cmake_args' from command line to 'None'
[0.156s] Level 5:colcon.colcon_core.verb:set package 'motion_planning_pkg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.156s] DEBUG:colcon.colcon_core.verb:Building package 'motion_planning_pkg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg', 'merge_install': False, 'path': '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg', 'symlink_install': False, 'test_result_base': None}
[0.156s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.157s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.157s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg' with build type 'ament_python'
[0.157s] Level 1:colcon.colcon_core.shell:create_environment_hook('motion_planning_pkg', 'ament_prefix_path')
[0.159s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.159s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/share/motion_planning_pkg/hook/ament_prefix_path.ps1'
[0.160s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/share/motion_planning_pkg/hook/ament_prefix_path.dsv'
[0.160s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/share/motion_planning_pkg/hook/ament_prefix_path.sh'
[0.160s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.160s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.290s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg'
[0.290s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.290s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.612s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg': PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/motion_planning_pkg build --build-base /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build install --record /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log --single-version-externally-managed install_data
[0.921s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg' returned '0': PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/motion_planning_pkg build --build-base /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build install --record /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log --single-version-externally-managed install_data
[0.923s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg' for CMake module files
[0.924s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg' for CMake config files
[0.924s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib'
[0.924s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/bin'
[0.925s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/pkgconfig/motion_planning_pkg.pc'
[0.925s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages'
[0.925s] Level 1:colcon.colcon_core.shell:create_environment_hook('motion_planning_pkg', 'pythonpath')
[0.925s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/share/motion_planning_pkg/hook/pythonpath.ps1'
[0.926s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/share/motion_planning_pkg/hook/pythonpath.dsv'
[0.926s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/share/motion_planning_pkg/hook/pythonpath.sh'
[0.926s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/bin'
[0.926s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(motion_planning_pkg)
[0.927s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/share/motion_planning_pkg/package.ps1'
[0.927s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/share/motion_planning_pkg/package.dsv'
[0.928s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/share/motion_planning_pkg/package.sh'
[0.929s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/share/motion_planning_pkg/package.bash'
[0.929s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/share/motion_planning_pkg/package.zsh'
[0.930s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/share/colcon-core/packages/motion_planning_pkg)
[0.930s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.930s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.931s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.931s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.935s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.935s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.935s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.946s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.947s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ccag/ccag_ws/install/local_setup.ps1'
[0.948s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ccag/ccag_ws/install/_local_setup_util_ps1.py'
[0.949s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ccag/ccag_ws/install/setup.ps1'
[0.950s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ccag/ccag_ws/install/local_setup.sh'
[0.951s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ccag/ccag_ws/install/_local_setup_util_sh.py'
[0.951s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ccag/ccag_ws/install/setup.sh'
[0.953s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ccag/ccag_ws/install/local_setup.bash'
[0.953s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ccag/ccag_ws/install/setup.bash'
[0.954s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ccag/ccag_ws/install/local_setup.zsh'
[0.955s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ccag/ccag_ws/install/setup.zsh'
