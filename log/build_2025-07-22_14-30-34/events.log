[0.000000] (-) TimerEvent: {}
[0.000475] (-) JobUnselected: {'identifier': 'camera_calibration'}
[0.000627] (-) JobUnselected: {'identifier': 'data_interface'}
[0.000698] (-) JobUnselected: {'identifier': 'handeye_realsense'}
[0.000727] (-) JobUnselected: {'identifier': 'motion_planning_pkg'}
[0.000801] (-) JobUnselected: {'identifier': 'mujoco_sim'}
[0.000821] (-) JobUnselected: {'identifier': 'pose_estimation'}
[0.000842] (-) JobUnselected: {'identifier': 'pose_estimation_client'}
[0.000861] (-) JobUnselected: {'identifier': 'qt_ros_test'}
[0.000880] (-) JobUnselected: {'identifier': 'yolo_bringup'}
[0.000899] (-) JobUnselected: {'identifier': 'yolo_msgs'}
[0.000928] (-) JobUnselected: {'identifier': 'yolo_ros'}
[0.000956] (detected_post_processing) JobQueued: {'identifier': 'detected_post_processing', 'dependencies': OrderedDict([('data_interface', '/home/<USER>/ccag/ccag_ws/install/data_interface')])}
[0.001010] (detected_post_processing) JobStarted: {'identifier': 'detected_post_processing'}
[0.008451] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'cmake'}
[0.009043] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'build'}
[0.009720] (detected_post_processing) Command: {'cmd': ['/usr/local/bin/cmake', '--build', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', '--', '-j28', '-l28'], 'cwd': '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh:en_US:en'), ('USER', 'jsy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/terminator.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1965'), ('SYSTEMD_EXEC_PID', '2309'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '6400'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'jsy'), ('JOURNAL_STREAM', '8:19570'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu.default.path'), ('USERNAME', 'jsy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/condabin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/sunshine:@/tmp/.ICE-unix/2285,unix/sunshine:/tmp/.ICE-unix/2285'), ('INVOCATION_ID', '409fa1a318694d1bbe1023ff64ade7fb'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('IBUS_DISABLE_SNOOPER', '1'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:f8b6b3e0-8d20-4fa8-9156-26c45d57c423'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ccag/ccag_ws/install/yolo_ros/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/handeye_realsense/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/camera_calibration/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble')]), 'shell': False}
[0.027950] (detected_post_processing) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.099890] (-) TimerEvent: {}
[0.131892] (detected_post_processing) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.150313] (detected_post_processing) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.151925] (detected_post_processing) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.155644] (detected_post_processing) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.161895] (detected_post_processing) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.169847] (detected_post_processing) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.191341] (detected_post_processing) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.192286] (detected_post_processing) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.200065] (-) TimerEvent: {}
[0.266559] (detected_post_processing) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.300213] (-) TimerEvent: {}
[0.301983] (detected_post_processing) StdoutLine: {'line': b'-- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[0.320106] (detected_post_processing) StdoutLine: {'line': b'-- Found vision_msgs: 4.1.1 (/opt/ros/humble/share/vision_msgs/cmake)\n'}
[0.329929] (detected_post_processing) StdoutLine: {'line': b'-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)\n'}
[0.338506] (detected_post_processing) StdoutLine: {'line': b'-- Found message_filters: 4.3.7 (/opt/ros/humble/share/message_filters/cmake)\n'}
[0.340206] (detected_post_processing) StdoutLine: {'line': b'-- Found tf2: 0.25.12 (/opt/ros/humble/share/tf2/cmake)\n'}
[0.342579] (detected_post_processing) StdoutLine: {'line': b'-- Found tf2_ros: 0.25.12 (/opt/ros/humble/share/tf2_ros/cmake)\n'}
[0.390657] (detected_post_processing) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.25.12 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)\n'}
[0.392662] (detected_post_processing) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[0.393541] (detected_post_processing) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[0.394489] (detected_post_processing) StdoutLine: {'line': b'-- Found data_interface: 0.0.0 (/home/<USER>/ccag/ccag_ws/install/data_interface/share/data_interface/cmake)\n'}
[0.400352] (-) TimerEvent: {}
[0.407519] (detected_post_processing) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[0.458169] (detected_post_processing) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[0.458336] (detected_post_processing) StdoutLine: {'line': b'-- Configured cppcheck include dirs: \n'}
[0.458380] (detected_post_processing) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[0.458435] (detected_post_processing) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[0.458905] (detected_post_processing) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[0.458952] (detected_post_processing) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[0.459108] (detected_post_processing) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[0.459617] (detected_post_processing) StdoutLine: {'line': b'-- Configuring done\n'}
[0.472449] (detected_post_processing) StdoutLine: {'line': b'-- Generating done\n'}
[0.477038] (detected_post_processing) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ccag/ccag_ws/build/detected_post_processing\n'}
[0.498340] (detected_post_processing) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target test_post_processing\x1b[0m\n'}
[0.500097] (detected_post_processing) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target post_processing\x1b[0m\n'}
[0.500409] (-) TimerEvent: {}
[0.510912] (detected_post_processing) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/test_post_processing.dir/src/test.cpp.o\x1b[0m\n'}
[0.517402] (detected_post_processing) StdoutLine: {'line': b'[ 75%] Built target post_processing\n'}
[0.600533] (-) TimerEvent: {}
[0.700841] (-) TimerEvent: {}
[0.801173] (-) TimerEvent: {}
[0.901382] (-) TimerEvent: {}
[1.001616] (-) TimerEvent: {}
[1.101893] (-) TimerEvent: {}
[1.202127] (-) TimerEvent: {}
[1.302403] (-) TimerEvent: {}
[1.402659] (-) TimerEvent: {}
[1.502944] (-) TimerEvent: {}
[1.603276] (-) TimerEvent: {}
[1.703550] (-) TimerEvent: {}
[1.803786] (-) TimerEvent: {}
[1.904036] (-) TimerEvent: {}
[2.004296] (-) TimerEvent: {}
[2.104563] (-) TimerEvent: {}
[2.204820] (-) TimerEvent: {}
[2.305077] (-) TimerEvent: {}
[2.317168] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid find_three_points(const std::vector<cv::Vec<float, 3> >&, std::vector<cv::Point_<float> >&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[2.317459] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:62:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KbottomLabel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[2.317576] (detected_post_processing) StderrLine: {'line': b'   62 |     int \x1b[01;35m\x1b[KbottomLabel\x1b[m\x1b[K = 1 - topLabel;\n'}
[2.317614] (detected_post_processing) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[2.318738] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[2.318834] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:95:14:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kargc\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[2.318874] (detected_post_processing) StderrLine: {'line': b'   95 | int main(\x1b[01;35m\x1b[Kint argc\x1b[m\x1b[K, char *argv[]){\n'}
[2.318903] (detected_post_processing) StderrLine: {'line': b'      |          \x1b[01;35m\x1b[K~~~~^~~~\x1b[m\x1b[K\n'}
[2.318930] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:95:26:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kargv\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[2.318960] (detected_post_processing) StderrLine: {'line': b'   95 | int main(int argc, \x1b[01;35m\x1b[Kchar *argv[]\x1b[m\x1b[K){\n'}
[2.318989] (detected_post_processing) StderrLine: {'line': b'      |                    \x1b[01;35m\x1b[K~~~~~~^~~~~~\x1b[m\x1b[K\n'}
[2.405203] (-) TimerEvent: {}
[2.505518] (-) TimerEvent: {}
[2.605781] (-) TimerEvent: {}
[2.706041] (-) TimerEvent: {}
[2.806330] (-) TimerEvent: {}
[2.895732] (detected_post_processing) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable test_post_processing\x1b[0m\n'}
[2.906512] (-) TimerEvent: {}
[3.006778] (-) TimerEvent: {}
[3.107011] (-) TimerEvent: {}
[3.207355] (-) TimerEvent: {}
[3.248970] (detected_post_processing) StdoutLine: {'line': b'[100%] Built target test_post_processing\n'}
[3.257470] (detected_post_processing) CommandEnded: {'returncode': 0}
[3.258083] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'install'}
[3.262289] (detected_post_processing) Command: {'cmd': ['/usr/local/bin/cmake', '--install', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing'], 'cwd': '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh:en_US:en'), ('USER', 'jsy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/terminator.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1965'), ('SYSTEMD_EXEC_PID', '2309'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '6400'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'jsy'), ('JOURNAL_STREAM', '8:19570'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu.default.path'), ('USERNAME', 'jsy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/condabin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/sunshine:@/tmp/.ICE-unix/2285,unix/sunshine:/tmp/.ICE-unix/2285'), ('INVOCATION_ID', '409fa1a318694d1bbe1023ff64ade7fb'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('IBUS_DISABLE_SNOOPER', '1'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:f8b6b3e0-8d20-4fa8-9156-26c45d57c423'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ccag/ccag_ws/install/yolo_ros/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/handeye_realsense/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/camera_calibration/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble')]), 'shell': False}
[3.266311] (detected_post_processing) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[3.266564] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing\n'}
[3.266801] (detected_post_processing) StdoutLine: {'line': b'-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/test_post_processing\n'}
[3.267174] (detected_post_processing) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/test_post_processing" to ""\n'}
[3.267403] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config\n'}
[3.267475] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config/camera_info.yaml\n'}
[3.267514] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/package_run_dependencies/detected_post_processing\n'}
[3.267548] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/parent_prefix_path/detected_post_processing\n'}
[3.267583] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.sh\n'}
[3.267614] (detected_post_processing) StdoutLine: {'line': b'-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.dsv\n'}
[3.267711] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.sh\n'}
[3.267751] (detected_post_processing) StdoutLine: {'line': b'-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.dsv\n'}
[3.267899] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.bash\n'}
[3.267939] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.sh\n'}
[3.267969] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.zsh\n'}
[3.268030] (detected_post_processing) StdoutLine: {'line': b'-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.dsv\n'}
[3.268079] (detected_post_processing) StdoutLine: {'line': b'-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.dsv\n'}
[3.268133] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/packages/detected_post_processing\n'}
[3.268165] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig.cmake\n'}
[3.268195] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig-version.cmake\n'}
[3.268224] (detected_post_processing) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.xml\n'}
[3.269280] (detected_post_processing) CommandEnded: {'returncode': 0}
[3.279070] (detected_post_processing) JobEnded: {'identifier': 'detected_post_processing', 'rc': 0}
[3.279627] (-) EventReactorShutdown: {}
