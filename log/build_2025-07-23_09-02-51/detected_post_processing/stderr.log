[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kvoid find_three_points(const std::vector<cv::Vec<float, 3> >&, std::vector<cv::Point_<float> >&)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:64:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
   64 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
      |         [01;35m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kstd::vector<cv::Point_<float> > image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:127:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  127 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
      |         [01;35m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:443:14:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kargc[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  443 | int main([01;35m[Kint argc[m[K, char *argv[]){
      |          [01;35m[K~~~~^~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:443:26:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kargv[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  443 | int main(int argc, [01;35m[Kchar *argv[][m[K){
      |                    [01;35m[K~~~~~~^~~~~~[m[K
