[0.523s] Invoking command in '/home/<USER>/ccag/ccag_ws/src/mujoco_sim': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/mujoco_sim/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/mujoco_sim build --build-base /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build install --record /home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log --single-version-externally-managed install_data
[0.666s] running egg_info
[0.675s] writing ../../build/mujoco_sim/mujoco_sim.egg-info/PKG-INFO
[0.675s] writing dependency_links to ../../build/mujoco_sim/mujoco_sim.egg-info/dependency_links.txt
[0.675s] writing entry points to ../../build/mujoco_sim/mujoco_sim.egg-info/entry_points.txt
[0.675s] writing requirements to ../../build/mujoco_sim/mujoco_sim.egg-info/requires.txt
[0.675s] writing top-level names to ../../build/mujoco_sim/mujoco_sim.egg-info/top_level.txt
[0.693s] reading manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'
[0.694s] writing manifest file '../../build/mujoco_sim/mujoco_sim.egg-info/SOURCES.txt'
[0.694s] running build
[0.694s] running build_py
[0.694s] copying mujoco_sim/panda_sim.py -> /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build/lib/mujoco_sim
[0.695s] running install
[0.698s] running install_lib
[0.707s] copying /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build/lib/mujoco_sim/panda_sim.py -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim
[0.707s] byte-compiling /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim/panda_sim.py to panda_sim.cpython-310.pyc
[0.709s] running install_data
[0.709s] copying launch/mujoco_sim.launch.py -> /home/<USER>/ccag/ccag_ws/install/mujoco_sim/share/mujoco_sim/launch
[0.710s] running install_egg_info
[0.719s] removing '/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim-0.0.0-py3.10.egg-info' (and everything under it)
[0.719s] Copying ../../build/mujoco_sim/mujoco_sim.egg-info to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages/mujoco_sim-0.0.0-py3.10.egg-info
[0.720s] running install_scripts
[0.810s] Installing init_panda script to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/mujoco_sim
[0.810s] Installing panda_sim script to /home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/mujoco_sim
[0.810s] writing list of installed files to '/home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log'
[0.827s] Invoked command in '/home/<USER>/ccag/ccag_ws/src/mujoco_sim' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/mujoco_sim/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/mujoco_sim build --build-base /home/<USER>/ccag/ccag_ws/build/mujoco_sim/build install --record /home/<USER>/ccag/ccag_ws/build/mujoco_sim/install.log --single-version-externally-managed install_data
