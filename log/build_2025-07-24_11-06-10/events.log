[0.000000] (-) TimerEvent: {}
[0.000195] (-) JobUnselected: {'identifier': 'camera_calibration'}
[0.000220] (-) JobUnselected: {'identifier': 'data_interface'}
[0.000232] (-) JobUnselected: {'identifier': 'handeye_realsense'}
[0.000246] (-) JobUnselected: {'identifier': 'motion_planning_pkg'}
[0.000254] (-) JobUnselected: {'identifier': 'mujoco_sim'}
[0.000263] (-) JobUnselected: {'identifier': 'pose_estimation'}
[0.000451] (-) JobUnselected: {'identifier': 'pose_estimation_client'}
[0.000461] (-) JobUnselected: {'identifier': 'qt_ros_test'}
[0.000547] (-) JobUnselected: {'identifier': 'yolo_bringup'}
[0.000556] (-) JobUnselected: {'identifier': 'yolo_msgs'}
[0.000564] (-) JobUnselected: {'identifier': 'yolo_ros'}
[0.000654] (detected_post_processing) JobQueued: {'identifier': 'detected_post_processing', 'dependencies': OrderedDict([('data_interface', '/home/<USER>/ccag/ccag_ws/install/data_interface')])}
[0.000668] (detected_post_processing) JobStarted: {'identifier': 'detected_post_processing'}
[0.008142] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'cmake'}
[0.008699] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'build'}
[0.009321] (detected_post_processing) Command: {'cmd': ['/usr/local/bin/cmake', '--build', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', '--', '-j28', '-l28'], 'cwd': '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh:en_US:en'), ('USER', 'jsy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/terminator.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '2023'), ('SYSTEMD_EXEC_PID', '2368'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '42923'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'jsy'), ('JOURNAL_STREAM', '8:16613'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu.default.path'), ('USERNAME', 'jsy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/condabin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/sunshine:@/tmp/.ICE-unix/2345,unix/sunshine:/tmp/.ICE-unix/2345'), ('INVOCATION_ID', '32d91192a35b4c32b555d4829679bc6e'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('IBUS_DISABLE_SNOOPER', '1'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:2f604913-2343-444d-b306-a7b5e33cab6b'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ccag/ccag_ws/install/yolo_ros/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/handeye_realsense/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/camera_calibration/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble')]), 'shell': False}
[0.058849] (detected_post_processing) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target post_processing\x1b[0m\n'}
[0.075354] (detected_post_processing) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/test_post_processing.dir/src/test.cpp.o\x1b[0m\n'}
[0.080411] (detected_post_processing) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o\x1b[0m\n'}
[0.099946] (-) TimerEvent: {}
[0.200199] (-) TimerEvent: {}
[0.294151] (detected_post_processing) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:21\x1b[m\x1b[K:\n'}
[0.294365] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp\x07-Wcpp\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.294441] (detected_post_processing) StderrLine: {'line': b'   35 | #\x1b[01;35m\x1b[Kwarning\x1b[m\x1b[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead\n'}
[0.294504] (detected_post_processing) StderrLine: {'line': b'      |  \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[0.300258] (-) TimerEvent: {}
[0.400431] (-) TimerEvent: {}
[0.500661] (-) TimerEvent: {}
[0.601043] (-) TimerEvent: {}
[0.701343] (-) TimerEvent: {}
[0.801605] (-) TimerEvent: {}
[0.901968] (-) TimerEvent: {}
[1.002191] (-) TimerEvent: {}
[1.102429] (-) TimerEvent: {}
[1.202714] (-) TimerEvent: {}
[1.303035] (-) TimerEvent: {}
[1.403317] (-) TimerEvent: {}
[1.503590] (-) TimerEvent: {}
[1.603851] (-) TimerEvent: {}
[1.704216] (-) TimerEvent: {}
[1.804458] (-) TimerEvent: {}
[1.891619] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid find_three_points(const std::vector<cv::Vec<float, 3> >&, std::vector<cv::Point_<float> >&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.891761] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:64:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KbottomLabel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.891803] (detected_post_processing) StderrLine: {'line': b'   64 |     int \x1b[01;35m\x1b[KbottomLabel\x1b[m\x1b[K = 1 - topLabel;\n'}
[1.891837] (detected_post_processing) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.893405] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<cv::Point_<float> > image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.893490] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:127:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KbottomLabel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.893529] (detected_post_processing) StderrLine: {'line': b'  127 |     int \x1b[01;35m\x1b[KbottomLabel\x1b[m\x1b[K = 1 - topLabel;\n'}
[1.893559] (detected_post_processing) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.904534] (-) TimerEvent: {}
[1.905063] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid calculatePixelTo3DScale(const cv::Mat&, const cv::Mat&, const cv::Mat&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.905184] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:300:45:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Krvec\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.905224] (detected_post_processing) StderrLine: {'line': b'  300 |                              \x1b[01;35m\x1b[Kconst cv::Mat& rvec\x1b[m\x1b[K) {\n'}
[1.905254] (detected_post_processing) StderrLine: {'line': b'      |                              \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~^~~~\x1b[m\x1b[K\n'}
[1.922472] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.922606] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:708:14:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kargc\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.922660] (detected_post_processing) StderrLine: {'line': b'  708 | int main(\x1b[01;35m\x1b[Kint argc\x1b[m\x1b[K, char *argv[]){\n'}
[1.922691] (detected_post_processing) StderrLine: {'line': b'      |          \x1b[01;35m\x1b[K~~~~^~~~\x1b[m\x1b[K\n'}
[1.922719] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:708:26:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kargv\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.922748] (detected_post_processing) StderrLine: {'line': b'  708 | int main(int argc, \x1b[01;35m\x1b[Kchar *argv[]\x1b[m\x1b[K){\n'}
[1.922776] (detected_post_processing) StderrLine: {'line': b'      |                    \x1b[01;35m\x1b[K~~~~~~^~~~~~\x1b[m\x1b[K\n'}
[2.004633] (-) TimerEvent: {}
[2.104974] (-) TimerEvent: {}
[2.205283] (-) TimerEvent: {}
[2.305539] (-) TimerEvent: {}
[2.405772] (-) TimerEvent: {}
[2.506103] (-) TimerEvent: {}
[2.606347] (-) TimerEvent: {}
[2.665879] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[2.666053] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:317:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KbottomLabel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[2.666113] (detected_post_processing) StderrLine: {'line': b'  317 |     int \x1b[01;35m\x1b[KbottomLabel\x1b[m\x1b[K = 1 - topLabel;\n'}
[2.666157] (detected_post_processing) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[2.706435] (-) TimerEvent: {}
[2.806701] (-) TimerEvent: {}
[2.906964] (-) TimerEvent: {}
[2.977589] (detected_post_processing) StdoutLine: {'line': b'[ 75%] \x1b[32m\x1b[1mLinking CXX executable test_post_processing\x1b[0m\n'}
[3.007057] (-) TimerEvent: {}
[3.107295] (-) TimerEvent: {}
[3.207552] (-) TimerEvent: {}
[3.307853] (detected_post_processing) StdoutLine: {'line': b'[ 75%] Built target test_post_processing\n'}
[3.308041] (-) TimerEvent: {}
[3.408269] (-) TimerEvent: {}
[3.508582] (-) TimerEvent: {}
[3.608911] (-) TimerEvent: {}
[3.709258] (-) TimerEvent: {}
[3.809668] (-) TimerEvent: {}
[3.909964] (-) TimerEvent: {}
[4.010272] (-) TimerEvent: {}
[4.110591] (-) TimerEvent: {}
[4.210876] (-) TimerEvent: {}
[4.311284] (-) TimerEvent: {}
[4.411758] (-) TimerEvent: {}
[4.512210] (-) TimerEvent: {}
[4.612503] (-) TimerEvent: {}
[4.712922] (-) TimerEvent: {}
[4.813325] (-) TimerEvent: {}
[4.913636] (-) TimerEvent: {}
[5.014028] (-) TimerEvent: {}
[5.114443] (-) TimerEvent: {}
[5.214871] (-) TimerEvent: {}
[5.315275] (-) TimerEvent: {}
[5.415697] (-) TimerEvent: {}
[5.516095] (-) TimerEvent: {}
[5.616532] (-) TimerEvent: {}
[5.716919] (-) TimerEvent: {}
[5.817340] (-) TimerEvent: {}
[5.917753] (-) TimerEvent: {}
[6.018165] (-) TimerEvent: {}
[6.118562] (-) TimerEvent: {}
[6.218957] (-) TimerEvent: {}
[6.319350] (-) TimerEvent: {}
[6.419742] (-) TimerEvent: {}
[6.520119] (-) TimerEvent: {}
[6.620499] (-) TimerEvent: {}
[6.720878] (-) TimerEvent: {}
[6.821269] (-) TimerEvent: {}
[6.921654] (-) TimerEvent: {}
[7.022033] (-) TimerEvent: {}
[7.122424] (-) TimerEvent: {}
[7.222811] (-) TimerEvent: {}
[7.323207] (-) TimerEvent: {}
[7.423596] (-) TimerEvent: {}
[7.524023] (-) TimerEvent: {}
[7.624354] (-) TimerEvent: {}
[7.724712] (-) TimerEvent: {}
[7.825099] (-) TimerEvent: {}
[7.925395] (-) TimerEvent: {}
[8.025790] (-) TimerEvent: {}
[8.126201] (-) TimerEvent: {}
[8.226618] (-) TimerEvent: {}
[8.327019] (-) TimerEvent: {}
[8.427468] (-) TimerEvent: {}
[8.527864] (-) TimerEvent: {}
[8.628310] (-) TimerEvent: {}
[8.728733] (-) TimerEvent: {}
[8.829317] (-) TimerEvent: {}
[8.929658] (-) TimerEvent: {}
[9.029955] (-) TimerEvent: {}
[9.130179] (-) TimerEvent: {}
[9.230400] (-) TimerEvent: {}
[9.330603] (-) TimerEvent: {}
[9.430801] (-) TimerEvent: {}
[9.530995] (-) TimerEvent: {}
[9.631230] (-) TimerEvent: {}
[9.731451] (-) TimerEvent: {}
[9.831692] (-) TimerEvent: {}
