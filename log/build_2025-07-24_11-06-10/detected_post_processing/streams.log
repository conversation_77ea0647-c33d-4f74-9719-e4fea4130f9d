[0.009s] Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
[0.058s] [35m[1mConsolidate compiler generated dependencies of target post_processing[0m
[0.075s] [ 25%] [32mBuilding CXX object CMakeFiles/test_post_processing.dir/src/test.cpp.o[0m
[0.080s] [ 50%] [32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o[0m
[0.294s] In file included from [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:21[m[K:
[0.294s] [01m[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:[m[K [01;35m[Kwarning: [m[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp-Wcpp]8;;[m[K]
[0.294s]    35 | #[01;35m[Kwarning[m[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead
[0.294s]       |  [01;35m[K^~~~~~~[m[K
[1.891s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kvoid find_three_points(const std::vector<cv::Vec<float, 3> >&, std::vector<cv::Point_<float> >&)[m[K’:
[1.891s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:64:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[1.891s]    64 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
[1.891s]       |         [01;35m[K^~~~~~~~~~~[m[K
[1.893s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kstd::vector<cv::Point_<float> > image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[1.893s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:127:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[1.893s]   127 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
[1.893s]       |         [01;35m[K^~~~~~~~~~~[m[K
[1.904s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kvoid calculatePixelTo3DScale(const cv::Mat&, const cv::Mat&, const cv::Mat&)[m[K’:
[1.905s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:300:45:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Krvec[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[1.905s]   300 |                              [01;35m[Kconst cv::Mat& rvec[m[K) {
[1.905s]       |                              [01;35m[K~~~~~~~~~~~~~~~^~~~[m[K
[1.922s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[1.922s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:708:14:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kargc[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[1.922s]   708 | int main([01;35m[Kint argc[m[K, char *argv[]){
[1.922s]       |          [01;35m[K~~~~^~~~[m[K
[1.922s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:708:26:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kargv[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[1.922s]   708 | int main(int argc, [01;35m[Kchar *argv[][m[K){
[1.922s]       |                    [01;35m[K~~~~~~^~~~~~[m[K
[2.665s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:[m[K In member function ‘[01m[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[2.665s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:317:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[2.665s]   317 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
[2.666s]       |         [01;35m[K^~~~~~~~~~~[m[K
[2.977s] [ 75%] [32m[1mLinking CXX executable test_post_processing[0m
[3.307s] [ 75%] Built target test_post_processing
[10.146s] [100%] [32m[1mLinking CXX executable post_processing[0m
[10.905s] [100%] Built target post_processing
[10.916s] Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
[10.923s] Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --install /home/<USER>/ccag/ccag_ws/build/detected_post_processing
[10.926s] -- Install configuration: ""
[10.926s] -- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing
[10.934s] -- Set non-toolchain portion of runtime path of "/home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing" to ""
[10.934s] -- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/test_post_processing
[10.935s] -- Set non-toolchain portion of runtime path of "/home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/test_post_processing" to ""
[10.935s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config
[10.935s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config/camera_info.yaml
[10.935s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/package_run_dependencies/detected_post_processing
[10.935s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/parent_prefix_path/detected_post_processing
[10.935s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.sh
[10.935s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.dsv
[10.936s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.sh
[10.936s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.dsv
[10.936s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.bash
[10.936s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.sh
[10.936s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.zsh
[10.937s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.dsv
[10.938s] -- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.dsv
[10.938s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/packages/detected_post_processing
[10.938s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig.cmake
[10.938s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig-version.cmake
[10.938s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.xml
[10.938s] Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib /usr/local/bin/cmake --install /home/<USER>/ccag/ccag_ws/build/detected_post_processing
