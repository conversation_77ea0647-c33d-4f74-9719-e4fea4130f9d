[0.453s] Invoking command in '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg': PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/motion_planning_pkg build --build-base /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build install --record /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log --single-version-externally-managed install_data
[0.599s] running egg_info
[0.608s] writing ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/PKG-INFO
[0.608s] writing dependency_links to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/dependency_links.txt
[0.608s] writing entry points to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/entry_points.txt
[0.608s] writing requirements to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/requires.txt
[0.608s] writing top-level names to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/top_level.txt
[0.625s] reading manifest file '../../build/motion_planning_pkg/motion_planning_pkg.egg-info/SOURCES.txt'
[0.626s] writing manifest file '../../build/motion_planning_pkg/motion_planning_pkg.egg-info/SOURCES.txt'
[0.626s] running build
[0.626s] running build_py
[0.626s] copying motion_planning_pkg/motion_planning.py -> /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build/lib/motion_planning_pkg
[0.627s] running install
[0.630s] running install_lib
[0.639s] copying /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build/lib/motion_planning_pkg/motion_planning.py -> /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg
[0.640s] byte-compiling /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg/motion_planning.py to motion_planning.cpython-310.pyc
[0.642s] running install_data
[0.642s] running install_egg_info
[0.652s] removing '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg-0.0.0-py3.10.egg-info' (and everything under it)
[0.652s] Copying ../../build/motion_planning_pkg/motion_planning_pkg.egg-info to /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg-0.0.0-py3.10.egg-info
[0.652s] running install_scripts
[0.749s] Installing broad_localization_node script to /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/motion_planning_pkg
[0.749s] Installing forward_fk_node script to /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/motion_planning_pkg
[0.749s] writing list of installed files to '/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log'
[0.765s] Invoked command in '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg' returned '0': PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/motion_planning_pkg build --build-base /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build install --record /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log --single-version-externally-managed install_data
