[0.010s] Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': AMENT_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
[0.052s] [35m[1mConsolidate compiler generated dependencies of target test_post_processing[0m
[0.055s] [35m[1mConsolidate compiler generated dependencies of target post_processing[0m
[0.069s] [ 50%] Built target test_post_processing
[0.076s] [ 75%] [32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o[0m
[0.288s] In file included from [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:21[m[K:
[0.288s] [01m[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:[m[K [01;35m[Kwarning: [m[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp-Wcpp]8;;[m[K]
[0.288s]    35 | #[01;35m[Kwarning[m[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead
[0.288s]       |  [01;35m[K^~~~~~~[m[K
[2.697s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:[m[K In member function ‘[01m[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[2.697s] [01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:317:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[2.697s]   317 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
[2.697s]       |         [01;35m[K^~~~~~~~~~~[m[K
[10.266s] [100%] [32m[1mLinking CXX executable post_processing[0m
[11.021s] [100%] Built target post_processing
[11.031s] Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
[11.035s] Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': AMENT_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/bin/cmake --install /home/<USER>/ccag/ccag_ws/build/detected_post_processing
[11.038s] -- Install configuration: ""
[11.038s] -- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing
[11.044s] -- Set non-toolchain portion of runtime path of "/home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing" to ""
[11.044s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/test_post_processing
[11.044s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config
[11.044s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config/camera_info.yaml
[11.044s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/package_run_dependencies/detected_post_processing
[11.044s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/parent_prefix_path/detected_post_processing
[11.045s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.sh
[11.045s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.dsv
[11.045s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.sh
[11.045s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.dsv
[11.045s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.bash
[11.045s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.sh
[11.045s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.zsh
[11.045s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.dsv
[11.045s] -- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.dsv
[11.045s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/packages/detected_post_processing
[11.046s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig.cmake
[11.046s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig-version.cmake
[11.046s] -- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.xml
[11.046s] Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/bin/cmake --install /home/<USER>/ccag/ccag_ws/build/detected_post_processing
