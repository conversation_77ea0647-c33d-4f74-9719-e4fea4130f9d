Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': AMENT_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/bin/cmake --build /home/<USER>/ccag/ccag_ws/build/detected_post_processing -- -j28 -l28
Invoking command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing': AMENT_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/bin/cmake --install /home/<USER>/ccag/ccag_ws/build/detected_post_processing
Invoked command in '/home/<USER>/ccag/ccag_ws/build/detected_post_processing' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ccag/ccag_ws/install/data_interface/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/bin/cmake --install /home/<USER>/ccag/ccag_ws/build/detected_post_processing
