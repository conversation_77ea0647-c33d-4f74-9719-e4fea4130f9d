running egg_info
writing ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/PKG-INFO
writing dependency_links to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/dependency_links.txt
writing entry points to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/entry_points.txt
writing requirements to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/requires.txt
writing top-level names to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/top_level.txt
reading manifest file '../../build/motion_planning_pkg/motion_planning_pkg.egg-info/SOURCES.txt'
writing manifest file '../../build/motion_planning_pkg/motion_planning_pkg.egg-info/SOURCES.txt'
running build
running build_py
copying motion_planning_pkg/motion_planning.py -> /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build/lib/motion_planning_pkg
running install
running install_lib
copying /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build/lib/motion_planning_pkg/motion_planning.py -> /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg
byte-compiling /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg/motion_planning.py to motion_planning.cpython-310.pyc
running install_data
running install_egg_info
removing '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg-0.0.0-py3.10.egg-info' (and everything under it)
Copying ../../build/motion_planning_pkg/motion_planning_pkg.egg-info to /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg-0.0.0-py3.10.egg-info
running install_scripts
Installing broad_localization_node script to /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/motion_planning_pkg
Installing forward_fk_node script to /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/motion_planning_pkg
writing list of installed files to '/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log'
