[0.000000] (-) TimerEvent: {}
[0.000248] (-) JobUnselected: {'identifier': 'camera_calibration'}
[0.000283] (-) JobUnselected: {'identifier': 'data_interface'}
[0.000356] (-) JobUnselected: {'identifier': 'detected_post_processing'}
[0.000391] (-) JobUnselected: {'identifier': 'handeye_realsense'}
[0.000465] (-) JobUnselected: {'identifier': 'mujoco_sim'}
[0.000487] (-) JobUnselected: {'identifier': 'pose_estimation'}
[0.000508] (-) JobUnselected: {'identifier': 'pose_estimation_client'}
[0.000529] (-) JobUnselected: {'identifier': 'qt_ros_test'}
[0.000549] (-) JobUnselected: {'identifier': 'yolo_bringup'}
[0.000569] (-) JobUnselected: {'identifier': 'yolo_msgs'}
[0.000598] (-) JobUnselected: {'identifier': 'yolo_ros'}
[0.000629] (motion_planning_pkg) JobQueued: {'identifier': 'motion_planning_pkg', 'dependencies': OrderedDict()}
[0.000687] (motion_planning_pkg) JobStarted: {'identifier': 'motion_planning_pkg'}
[0.099411] (-) TimerEvent: {}
[0.199745] (-) TimerEvent: {}
[0.300015] (-) TimerEvent: {}
[0.400275] (-) TimerEvent: {}
[0.445481] (motion_planning_pkg) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/motion_planning_pkg', 'build', '--build-base', '/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build', 'install', '--record', '/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg', 'env': {'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:zh:en_US:en', 'USER': 'jsy', 'LC_TIME': 'zh_CN.UTF-8', 'XDG_SESSION_TYPE': 'x11', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib', 'HOME': '/home/<USER>', 'CONDA_SHLVL': '0', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/terminator.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'LC_MONETARY': 'zh_CN.UTF-8', 'MANAGERPID': '2023', 'SYSTEMD_EXEC_PID': '2368', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', '_CE_M': '', 'TERMINATOR_DBUS_NAME': 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '42923', 'MANDATORY_PATH': '/usr/share/gconf/ubuntu.mandatory.path', 'IM_CONFIG_PHASE': '1', 'COLCON_PREFIX_PATH': '/home/<USER>/ccag/ccag_ws/install', 'ROS_DISTRO': 'humble', 'GTK_IM_MODULE': 'fcitx', 'LOGNAME': 'jsy', 'JOURNAL_STREAM': '8:16613', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'DEFAULTS_PATH': '/usr/share/gconf/ubuntu.default.path', 'USERNAME': 'jsy', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', '_CE_CONDA': '', 'ROS_LOCALHOST_ONLY': '0', 'WINDOWPATH': '2', 'PATH': '/home/<USER>/anaconda3/condabin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/sunshine:@/tmp/.ICE-unix/2345,unix/sunshine:/tmp/.ICE-unix/2345', 'INVOCATION_ID': '32d91192a35b4c32b555d4829679bc6e', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'zh_CN.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': ':1', 'TERMINATOR_DBUS_PATH': '/net/tenshu/Terminator2', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'IBUS_DISABLE_SNOOPER': '1', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'XMODIFIERS': '@im=fcitx', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/gdm/Xauthority', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'SSH_AGENT_LAUNCHER': 'gnome-keyring', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble', 'CONDA_PYTHON_EXE': '/home/<USER>/anaconda3/bin/python', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'TERMINATOR_UUID': 'urn:uuid:cb53819d-9f96-45c2-8b06-7c1675262005', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'GPG_AGENT_INFO': '/run/user/1000/gnupg/S.gpg-agent:0:1', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'QT_IM_MODULE': 'fcitx', 'PWD': '/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'CONDA_EXE': '/home/<USER>/anaconda3/bin/conda', 'CLUTTER_IM_MODULE': 'fcitx', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/yolo_ros/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/handeye_realsense/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/camera_calibration/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'LC_NUMERIC': 'zh_CN.UTF-8', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1', 'VTE_VERSION': '6800', 'CMAKE_PREFIX_PATH': '/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/data_interface'}, 'shell': False}
[0.500394] (-) TimerEvent: {}
[0.596127] (motion_planning_pkg) StdoutLine: {'line': b'running egg_info\n'}
[0.600473] (-) TimerEvent: {}
[0.604244] (motion_planning_pkg) StdoutLine: {'line': b'writing ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/PKG-INFO\n'}
[0.604492] (motion_planning_pkg) StdoutLine: {'line': b'writing dependency_links to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/dependency_links.txt\n'}
[0.604588] (motion_planning_pkg) StdoutLine: {'line': b'writing entry points to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/entry_points.txt\n'}
[0.604761] (motion_planning_pkg) StdoutLine: {'line': b'writing requirements to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/requires.txt\n'}
[0.604831] (motion_planning_pkg) StdoutLine: {'line': b'writing top-level names to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/top_level.txt\n'}
[0.621430] (motion_planning_pkg) StdoutLine: {'line': b"reading manifest file '../../build/motion_planning_pkg/motion_planning_pkg.egg-info/SOURCES.txt'\n"}
[0.622058] (motion_planning_pkg) StdoutLine: {'line': b"writing manifest file '../../build/motion_planning_pkg/motion_planning_pkg.egg-info/SOURCES.txt'\n"}
[0.622158] (motion_planning_pkg) StdoutLine: {'line': b'running build\n'}
[0.622224] (motion_planning_pkg) StdoutLine: {'line': b'running build_py\n'}
[0.622431] (motion_planning_pkg) StdoutLine: {'line': b'copying motion_planning_pkg/motion_planning.py -> /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build/lib/motion_planning_pkg\n'}
[0.622595] (motion_planning_pkg) StdoutLine: {'line': b'running install\n'}
[0.626091] (motion_planning_pkg) StdoutLine: {'line': b'running install_lib\n'}
[0.634531] (motion_planning_pkg) StdoutLine: {'line': b'copying /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build/lib/motion_planning_pkg/motion_planning.py -> /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg\n'}
[0.634981] (motion_planning_pkg) StdoutLine: {'line': b'byte-compiling /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg/motion_planning.py to motion_planning.cpython-310.pyc\n'}
[0.637704] (motion_planning_pkg) StdoutLine: {'line': b'running install_data\n'}
[0.637885] (motion_planning_pkg) StdoutLine: {'line': b'running install_egg_info\n'}
[0.646646] (motion_planning_pkg) StdoutLine: {'line': b"removing '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg-0.0.0-py3.10.egg-info' (and everything under it)\n"}
[0.646814] (motion_planning_pkg) StdoutLine: {'line': b'Copying ../../build/motion_planning_pkg/motion_planning_pkg.egg-info to /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg-0.0.0-py3.10.egg-info\n'}
[0.647174] (motion_planning_pkg) StdoutLine: {'line': b'running install_scripts\n'}
[0.700678] (-) TimerEvent: {}
[0.738324] (motion_planning_pkg) StdoutLine: {'line': b'Installing broad_localization_node script to /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/motion_planning_pkg\n'}
[0.738611] (motion_planning_pkg) StdoutLine: {'line': b'Installing forward_fk_node script to /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/motion_planning_pkg\n'}
[0.738788] (motion_planning_pkg) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log'\n"}
[0.756785] (motion_planning_pkg) CommandEnded: {'returncode': 0}
[0.763496] (motion_planning_pkg) JobEnded: {'identifier': 'motion_planning_pkg', 'rc': 0}
[0.763980] (-) EventReactorShutdown: {}
