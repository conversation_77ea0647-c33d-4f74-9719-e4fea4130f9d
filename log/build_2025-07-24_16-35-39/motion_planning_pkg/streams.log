[0.446s] Invoking command in '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg': PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/motion_planning_pkg build --build-base /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build install --record /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log --single-version-externally-managed install_data
[0.596s] running egg_info
[0.604s] writing ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/PKG-INFO
[0.604s] writing dependency_links to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/dependency_links.txt
[0.604s] writing entry points to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/entry_points.txt
[0.604s] writing requirements to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/requires.txt
[0.604s] writing top-level names to ../../build/motion_planning_pkg/motion_planning_pkg.egg-info/top_level.txt
[0.621s] reading manifest file '../../build/motion_planning_pkg/motion_planning_pkg.egg-info/SOURCES.txt'
[0.621s] writing manifest file '../../build/motion_planning_pkg/motion_planning_pkg.egg-info/SOURCES.txt'
[0.621s] running build
[0.622s] running build_py
[0.622s] copying motion_planning_pkg/motion_planning.py -> /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build/lib/motion_planning_pkg
[0.622s] running install
[0.625s] running install_lib
[0.634s] copying /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build/lib/motion_planning_pkg/motion_planning.py -> /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg
[0.634s] byte-compiling /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg/motion_planning.py to motion_planning.cpython-310.pyc
[0.637s] running install_data
[0.637s] running install_egg_info
[0.646s] removing '/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg-0.0.0-py3.10.egg-info' (and everything under it)
[0.646s] Copying ../../build/motion_planning_pkg/motion_planning_pkg.egg-info to /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages/motion_planning_pkg-0.0.0-py3.10.egg-info
[0.647s] running install_scripts
[0.738s] Installing broad_localization_node script to /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/motion_planning_pkg
[0.738s] Installing forward_fk_node script to /home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/motion_planning_pkg
[0.738s] writing list of installed files to '/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log'
[0.756s] Invoked command in '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg' returned '0': PYTHONPATH=/home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/motion_planning_pkg build --build-base /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/build install --record /home/<USER>/ccag/ccag_ws/build/motion_planning_pkg/install.log --single-version-externally-managed install_data
