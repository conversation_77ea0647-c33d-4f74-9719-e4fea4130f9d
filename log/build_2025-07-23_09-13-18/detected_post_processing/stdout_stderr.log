[35m[1mConsolidate compiler generated dependencies of target test_post_processing[0m
[ 50%] Built target post_processing
[ 75%] [32mBuilding CXX object CMakeFiles/test_post_processing.dir/src/test.cpp.o[0m
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kvoid find_three_points(const std::vector<cv::Vec<float, 3> >&, std::vector<cv::Point_<float> >&)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:64:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
   64 |     int [01;35m[<PERSON><PERSON><PERSON><PERSON><PERSON>l[m[K = 1 - topLabel;
      |         [01;35m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kstd::vector<cv::Point_<float> > image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:127:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KbottomLabel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  127 |     int [01;35m[KbottomLabel[m[K = 1 - topLabel;
      |         [01;35m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kvoid calculatePixelTo3DScale(const cv::Mat&, const cv::Mat&, const cv::Mat&)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:300:45:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Krvec[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  300 |                              [01;35m[Kconst cv::Mat& rvec[m[K) {
      |                              [01;35m[K~~~~~~~~~~~~~~~^~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:554:14:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kargc[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  554 | int main([01;35m[Kint argc[m[K, char *argv[]){
      |          [01;35m[K~~~~^~~~[m[K
[01m[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/test.cpp:554:26:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kargv[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  554 | int main(int argc, [01;35m[Kchar *argv[][m[K){
      |                    [01;35m[K~~~~~~^~~~~~[m[K
[100%] [32m[1mLinking CXX executable test_post_processing[0m
[100%] Built target test_post_processing
-- Install configuration: ""
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/post_processing
-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/test_post_processing
-- Set non-toolchain portion of runtime path of "/home/<USER>/ccag/ccag_ws/install/detected_post_processing/lib/detected_post_processing/test_post_processing" to ""
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/config/camera_info.yaml
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/package_run_dependencies/detected_post_processing
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/parent_prefix_path/detected_post_processing
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.sh
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.sh
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/environment/path.dsv
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.bash
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.sh
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.zsh
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/local_setup.dsv
-- Installing: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.dsv
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/ament_index/resource_index/packages/detected_post_processing
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig.cmake
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/cmake/detected_post_processingConfig-version.cmake
-- Up-to-date: /home/<USER>/ccag/ccag_ws/install/detected_post_processing/share/detected_post_processing/package.xml
