[0.000000] (-) TimerEvent: {}
[0.000642] (-) JobUnselected: {'identifier': 'camera_calibration'}
[0.000733] (-) JobUnselected: {'identifier': 'data_interface'}
[0.000758] (-) JobUnselected: {'identifier': 'handeye_realsense'}
[0.000782] (-) JobUnselected: {'identifier': 'motion_planning_pkg'}
[0.000801] (-) JobUnselected: {'identifier': 'mujoco_sim'}
[0.000833] (-) JobUnselected: {'identifier': 'pose_estimation'}
[0.000891] (-) JobUnselected: {'identifier': 'pose_estimation_client'}
[0.000911] (-) JobUnselected: {'identifier': 'qt_ros_test'}
[0.000959] (-) JobUnselected: {'identifier': 'yolo_bringup'}
[0.000991] (-) JobUnselected: {'identifier': 'yolo_msgs'}
[0.001011] (-) JobUnselected: {'identifier': 'yolo_ros'}
[0.001072] (detected_post_processing) JobQueued: {'identifier': 'detected_post_processing', 'dependencies': OrderedDict([('data_interface', '/home/<USER>/ccag/ccag_ws/install/data_interface')])}
[0.001215] (detected_post_processing) JobStarted: {'identifier': 'detected_post_processing'}
[0.008874] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'cmake'}
[0.009426] (detected_post_processing) JobProgress: {'identifier': 'detected_post_processing', 'progress': 'build'}
[0.010145] (detected_post_processing) Command: {'cmd': ['/usr/local/bin/cmake', '--build', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', '--', '-j28', '-l28'], 'cwd': '/home/<USER>/ccag/ccag_ws/build/detected_post_processing', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh:en_US:en'), ('USER', 'jsy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface/lib:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/lib:/home/<USER>/ccag/ccag_ws/install/pose_estimation/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/terminator.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '2023'), ('SYSTEMD_EXEC_PID', '2369'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '24163'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu.mandatory.path'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'jsy'), ('JOURNAL_STREAM', '8:22697'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu.default.path'), ('USERNAME', 'jsy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/anaconda3/condabin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/sunshine:@/tmp/.ICE-unix/2345,unix/sunshine:/tmp/.ICE-unix/2345'), ('INVOCATION_ID', '979fa524968d42c7994f8f9e624a9e4b'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('IBUS_DISABLE_SNOOPER', '1'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/anaconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:41c04199-a39e-4a2b-aec2-5feeec265edf'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/ccag/ccag_ws/build/detected_post_processing'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/anaconda3/bin/conda'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ccag/ccag_ws/install/yolo_ros/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/yolo_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/mujoco_sim/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/handeye_realsense/lib/python3.10/site-packages:/home/<USER>/ccag/ccag_ws/install/data_interface/local/lib/python3.10/dist-packages:/home/<USER>/ccag/ccag_ws/install/camera_calibration/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ccag/ccag_ws/install/data_interface:/home/<USER>/ccag/ccag_ws/install/yolo_bringup:/home/<USER>/ccag/ccag_ws/install/yolo_msgs:/home/<USER>/ccag/ccag_ws/install/qt_ros_test:/home/<USER>/ccag/ccag_ws/install/pose_estimation_client:/home/<USER>/ccag/ccag_ws/install/pose_estimation:/home/<USER>/ccag/ccag_ws/install/detected_post_processing:/home/<USER>/ccag/ccag_ws/install/yolo_ros:/home/<USER>/ccag/ccag_ws/install/mujoco_sim:/home/<USER>/ccag/ccag_ws/install/motion_planning_pkg:/home/<USER>/ccag/ccag_ws/install/handeye_realsense:/home/<USER>/ccag/ccag_ws/install/camera_calibration:/opt/ros/humble')]), 'shell': False}
[0.056393] (detected_post_processing) StdoutLine: {'line': b'[ 50%] Built target test_post_processing\n'}
[0.057450] (detected_post_processing) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/post_processing.dir/src/post_processing.cpp.o\x1b[0m\n'}
[0.099921] (-) TimerEvent: {}
[0.200188] (-) TimerEvent: {}
[0.274525] (detected_post_processing) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:21\x1b[m\x1b[K:\n'}
[0.274767] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/tf2_geometry_msgs/tf2_geometry_msgs/tf2_geometry_msgs.h:35:2:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K#warning This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wcpp\x07-Wcpp\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.274849] (detected_post_processing) StderrLine: {'line': b'   35 | #\x1b[01;35m\x1b[Kwarning\x1b[m\x1b[K This header is obsolete, please include tf2_geometry_msgs/tf2_geometry_msgs.hpp instead\n'}
[0.274919] (detected_post_processing) StderrLine: {'line': b'      |  \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[0.300352] (-) TimerEvent: {}
[0.400747] (-) TimerEvent: {}
[0.501080] (-) TimerEvent: {}
[0.601381] (-) TimerEvent: {}
[0.701816] (-) TimerEvent: {}
[0.802256] (-) TimerEvent: {}
[0.902711] (-) TimerEvent: {}
[1.003191] (-) TimerEvent: {}
[1.103666] (-) TimerEvent: {}
[1.204081] (-) TimerEvent: {}
[1.304502] (-) TimerEvent: {}
[1.404936] (-) TimerEvent: {}
[1.505318] (-) TimerEvent: {}
[1.605573] (-) TimerEvent: {}
[1.705925] (-) TimerEvent: {}
[1.806263] (-) TimerEvent: {}
[1.906549] (-) TimerEvent: {}
[2.006883] (-) TimerEvent: {}
[2.107146] (-) TimerEvent: {}
[2.207433] (-) TimerEvent: {}
[2.307793] (-) TimerEvent: {}
[2.408125] (-) TimerEvent: {}
[2.508481] (-) TimerEvent: {}
[2.608788] (-) TimerEvent: {}
[2.709155] (-) TimerEvent: {}
[2.772273] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<cv::Point_<float> > DetectedPostProcessing::image_points_sort(const std::vector<cv::Vec<float, 3> >&, const Point2d&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[2.772474] (detected_post_processing) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ccag/ccag_ws/src/detected_post_processing/src/post_processing.cpp:317:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KbottomLabel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[2.772554] (detected_post_processing) StderrLine: {'line': b'  317 |     int \x1b[01;35m\x1b[KbottomLabel\x1b[m\x1b[K = 1 - topLabel;\n'}
[2.772623] (detected_post_processing) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[2.809309] (-) TimerEvent: {}
[2.909606] (-) TimerEvent: {}
[3.009896] (-) TimerEvent: {}
[3.110190] (-) TimerEvent: {}
[3.210493] (-) TimerEvent: {}
[3.310880] (-) TimerEvent: {}
[3.411291] (-) TimerEvent: {}
[3.511588] (-) TimerEvent: {}
[3.611894] (-) TimerEvent: {}
[3.712241] (-) TimerEvent: {}
[3.812558] (-) TimerEvent: {}
[3.912848] (-) TimerEvent: {}
[4.013244] (-) TimerEvent: {}
[4.113597] (-) TimerEvent: {}
[4.213899] (-) TimerEvent: {}
[4.314286] (-) TimerEvent: {}
[4.414601] (-) TimerEvent: {}
[4.514919] (-) TimerEvent: {}
[4.615225] (-) TimerEvent: {}
[4.715513] (-) TimerEvent: {}
[4.815926] (-) TimerEvent: {}
[4.916270] (-) TimerEvent: {}
[5.016608] (-) TimerEvent: {}
[5.116925] (-) TimerEvent: {}
[5.217262] (-) TimerEvent: {}
[5.317529] (-) TimerEvent: {}
[5.417837] (-) TimerEvent: {}
[5.518106] (-) TimerEvent: {}
[5.618413] (-) TimerEvent: {}
[5.718664] (-) TimerEvent: {}
[5.818910] (-) TimerEvent: {}
[5.919192] (-) TimerEvent: {}
[6.019434] (-) TimerEvent: {}
[6.119687] (-) TimerEvent: {}
[6.220023] (-) TimerEvent: {}
[6.320282] (-) TimerEvent: {}
[6.420561] (-) TimerEvent: {}
[6.520829] (-) TimerEvent: {}
[6.621064] (-) TimerEvent: {}
[6.721286] (-) TimerEvent: {}
[6.821536] (-) TimerEvent: {}
[6.921774] (-) TimerEvent: {}
[7.022080] (-) TimerEvent: {}
[7.122358] (-) TimerEvent: {}
[7.222619] (-) TimerEvent: {}
[7.322962] (-) TimerEvent: {}
[7.423280] (-) TimerEvent: {}
[7.523669] (-) TimerEvent: {}
[7.623921] (-) TimerEvent: {}
[7.724253] (-) TimerEvent: {}
[7.824478] (-) TimerEvent: {}
[7.924746] (-) TimerEvent: {}
[8.025083] (-) TimerEvent: {}
[8.125390] (-) TimerEvent: {}
[8.225668] (-) TimerEvent: {}
[8.325934] (-) TimerEvent: {}
[8.426319] (-) TimerEvent: {}
[8.526543] (-) TimerEvent: {}
[8.626773] (-) TimerEvent: {}
[8.727156] (-) TimerEvent: {}
[8.827546] (-) TimerEvent: {}
[8.928042] (-) TimerEvent: {}
[9.028341] (-) TimerEvent: {}
[9.128621] (-) TimerEvent: {}
[9.229032] (-) TimerEvent: {}
[9.329373] (-) TimerEvent: {}
[9.429650] (-) TimerEvent: {}
[9.529980] (-) TimerEvent: {}
[9.630286] (-) TimerEvent: {}
[9.730536] (-) TimerEvent: {}
[9.830878] (-) TimerEvent: {}
[9.931203] (-) TimerEvent: {}
[10.031455] (-) TimerEvent: {}
[10.131710] (-) TimerEvent: {}
[10.232042] (-) TimerEvent: {}
[10.332297] (-) TimerEvent: {}
[10.432563] (-) TimerEvent: {}
[10.532926] (-) TimerEvent: {}
[10.560338] (detected_post_processing) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable post_processing\x1b[0m\n'}
[10.633129] (-) TimerEvent: {}
[10.733464] (-) TimerEvent: {}
[10.833847] (-) TimerEvent: {}
[10.934136] (-) TimerEvent: {}
[11.034375] (-) TimerEvent: {}
[11.134623] (-) TimerEvent: {}
