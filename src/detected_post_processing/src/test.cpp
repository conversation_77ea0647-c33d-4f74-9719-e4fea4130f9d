#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <cv_bridge/cv_bridge.h>
#include <opencv2/opencv.hpp>
#include <iomanip>  // for std::setprecision
#include <numeric>  // for std::accumulate

// 判断三点是否共线
bool arePointsCollinear(const cv::Point2f& p1, const cv::Point2f& p2, const cv::Point2f& p3) {
    // 计算向量叉积 (p2 - p1) × (p3 - p1)
    // 如果叉积为0（或接近0，考虑浮点误差），则三点共线
    double crossProduct = (p2.x - p1.x) * (p3.y - p1.y) - 
                         (p2.y - p1.y) * (p3.x - p1.x);
    
    // 使用很小的epsilon来处理浮点精度问题
    const double epsilon = 1e-3;
    return std::abs(crossProduct) < epsilon;
}

void find_three_points(const std::vector<cv::Vec3f> &circles, std::vector<cv::Point2f> &non_collinear_points){
    
    // 转换为 cv::Mat 类型用于 kmeans
    cv::Mat data(circles.size(), 1, CV_32F);
    for (size_t i = 0; i < circles.size(); ++i) {
        data.at<float>(i, 0) = circles[i][1];
    }
    std::cout << "data:\n" << data << std::endl;
    // 聚类结果
    int K = 2;
    cv::Mat labels;
    cv::Mat centersMat;
    // 运行 K-means
    cv::kmeans(data, K, labels,
               cv::TermCriteria(cv::TermCriteria::EPS + cv::TermCriteria::MAX_ITER, 10, 1.0),
               3, cv::KMEANS_PP_CENTERS, centersMat);
    // 打印 centersMat 的值
    std::cout << "Centers:\n" << centersMat << std::endl;
    // 将聚类结果分组
    std::vector<cv::Point2f> groupTop;
    std::vector<cv::Point2f> groupBottom;
    std::vector<float> yAvg(K, 0);
    std::vector<int> yCount(K, 0);

    // 计算每个簇的平均 y 值
    for (int i = 0; i < labels.rows; ++i) {
        int label = labels.at<int>(i);
        yAvg[label] += circles[i][1];
        yCount[label]++;
    }
    for (int i = 0; i < K; ++i) {
        yAvg[i] /= yCount[i];
    }
    // 输出 yAvg 和 yCount 的值
    std::cout << "yAvg:\n";
    for (int i = 0; i < K; ++i) {
        std::cout << "Cluster " << i << ": " << yAvg[i] << std::endl;
    }
    std::cout << "yCount:\n";
    for (int i = 0; i < K; ++i) {
        std::cout << "Cluster " << i << ": " << yCount[i] << std::endl;
    }
    // 判断哪个是上（y 坐标小的是上面）
    int topLabel = yAvg[0] < yAvg[1] ? 0 : 1;
    int bottomLabel = 1 - topLabel;

    // 分开存放
    for (int i = 0; i < labels.rows; ++i) {
        int label = labels.at<int>(i);
        if (label == topLabel)
            groupTop.push_back(cv::Point2f(circles[i][0], circles[i][1]));
        else
            groupBottom.push_back(cv::Point2f(circles[i][0], circles[i][1]));
    }
    std::cout << "groupTop.size(): " << groupTop.size() << " groupBottom.size(): " << groupBottom.size() << std::endl;
    // 输出 groupTop 的值
    std::cout << "groupTop:\n";
    for (const auto& pt : groupTop) {
        std::cout << "(" << pt.x << ", " << pt.y << ")\n";
    }
    // 输出 groupBottom 的值
    std::cout << "groupBottom:\n";
    for (const auto& pt : groupBottom) {
        std::cout << "(" << pt.x << ", " << pt.y << ")\n";
    }
    // 从 groupTop 中取出两个圆心
    if (groupTop.size() >= 2) {
        non_collinear_points.push_back(groupTop[0]);
        non_collinear_points.push_back(groupTop[1]);
    }
    // 从 groupBottom 中取出一个圆心
    if (groupBottom.size() >= 1) {
        non_collinear_points.push_back(groupBottom[0]);
    }

}

std::vector<cv::Point2f> image_points_sort(const std::vector<cv::Vec3f> &circles, const cv::Point2d &pt1){
    // 转换为 cv::Mat 类型用于 kmeans
    cv::Mat data(circles.size(), 1, CV_32F);
    for (size_t i = 0; i < circles.size(); ++i) {
        data.at<float>(i, 0) = circles[i][1];
    }
    // 聚类结果
    int K = 2;
    cv::Mat labels;
    cv::Mat centersMat;
    // 运行 K-means
    cv::kmeans(data, K, labels,
                cv::TermCriteria(cv::TermCriteria::EPS + cv::TermCriteria::MAX_ITER, 10, 1.0),
                3, cv::KMEANS_PP_CENTERS, centersMat);
    // 将聚类结果分组
    std::vector<cv::Point2f> groupTop, groupBottom;
    std::vector<float> yAvg(K, 0);
    std::vector<int> yCount(K, 0);

    // 计算每个簇的平均 y 值
    for (int i = 0; i < labels.rows; ++i) {
        int label = labels.at<int>(i);
        yAvg[label] += circles[i][1];
        yCount[label]++;
    }
    for (int i = 0; i < K; ++i) {
        yAvg[i] /= yCount[i];
    }
    // 判断哪个是上（y 坐标小的是上面）
    int topLabel = yAvg[0] < yAvg[1] ? 0 : 1;
    int bottomLabel = 1 - topLabel;

    // 分开存放
    for (int i = 0; i < labels.rows; ++i) {
        int label = labels.at<int>(i);
        if (label == topLabel)
            groupTop.push_back(cv::Point2f(circles[i][0], circles[i][1]));
        else
            groupBottom.push_back(cv::Point2f(circles[i][0], circles[i][1]));
    }
    // std::cout << "groupTop.size(): " << groupTop.size() << " groupBottom.size(): " << groupBottom.size() << std::endl;
    std::sort(groupTop.begin(), groupTop.end(), [](const cv::Point2f& a, const cv::Point2f& b) {
        return a.x < b.x;  // 按x坐标升序排序
    });
    std::sort(groupBottom.begin(), groupBottom.end(), [](const cv::Point2f& a, const cv::Point2f& b) {
        return a.x < b.x;  // 按x坐标升序排序
    });
    // 定义2D点存放顺序
    std::vector<cv::Point2f> image_points;
    for (const auto& pt : groupTop) {
        image_points.push_back(cv::Point2d(pt1.x + pt.x, pt1.y + pt.y));
    }
    for (const auto& pt : groupBottom) {
        image_points.push_back(cv::Point2d(pt1.x + pt.x, pt1.y + pt.y));
    }
    // std::cout << "image_points:\n";
    // for (const auto& pt : image_points) {
    //     std::cout << "(" << pt.x << ", " << pt.y << ")\n";
    // }

    return image_points;
}

// 验证solvePnP结果的综合函数
void validatePnPResult(const std::vector<cv::Point3f>& object_points,
                      const std::vector<cv::Point2f>& image_points,
                      const cv::Mat& camera_matrix,
                      const cv::Mat& dist_coeffs,
                      const cv::Mat& rvec,
                      const cv::Mat& tvec,
                      cv::Mat& image) {

    std::cout << "\n==================== solvePnP 结果验证 ====================" << std::endl;

    // 1. 重投影误差验证
    std::vector<cv::Point2f> projected_points;
    cv::projectPoints(object_points, rvec, tvec, camera_matrix, dist_coeffs, projected_points);

    std::vector<double> errors;
    double total_error = 0.0;
    double max_error = 0.0;

    std::cout << "重投影误差详情:" << std::endl;
    for (size_t i = 0; i < image_points.size() && i < projected_points.size(); i++) {
        double error = cv::norm(image_points[i] - projected_points[i]);
        errors.push_back(error);
        total_error += error;
        max_error = std::max(max_error, error);

        std::cout << "  点" << i << ": 误差 " << std::fixed << std::setprecision(2)
                  << error << " 像素" << std::endl;
    }

    double mean_error = total_error / image_points.size();

    // 计算标准差
    double variance = 0.0;
    for (double error : errors) {
        variance += (error - mean_error) * (error - mean_error);
    }
    double std_dev = sqrt(variance / errors.size());

    std::cout << "重投影误差统计:" << std::endl;
    std::cout << "  平均误差: " << std::fixed << std::setprecision(3) << mean_error << " 像素" << std::endl;
    std::cout << "  最大误差: " << std::fixed << std::setprecision(3) << max_error << " 像素" << std::endl;
    std::cout << "  标准差: " << std::fixed << std::setprecision(3) << std_dev << " 像素" << std::endl;

    // 2. 几何一致性检查
    std::cout << "\n几何一致性检查:" << std::endl;

    // 检查投影点是否在图像范围内
    bool all_points_in_image = true;
    for (const auto& pt : projected_points) {
        if (pt.x < 0 || pt.x >= image.cols || pt.y < 0 || pt.y >= image.rows) {
            all_points_in_image = false;
            break;
        }
    }
    std::cout << "  投影点在图像范围内: " << (all_points_in_image ? "是" : "否") << std::endl;

    // 3. 位姿合理性检查
    std::cout << "\n位姿合理性检查:" << std::endl;
    double distance = cv::norm(tvec);
    std::cout << "  相机到目标距离: " << std::fixed << std::setprecision(3) << distance << " 米" << std::endl;

    // 检查距离是否合理（根据你的应用场景调整）
    if (distance > 0.1 && distance < 2.0) {
        std::cout << "  距离评估: 合理" << std::endl;
    } else {
        std::cout << "  距离评估: 可能不合理" << std::endl;
    }

    // 4. 旋转角度分析
    cv::Mat rotation_matrix;
    cv::Rodrigues(rvec, rotation_matrix);

    // 提取欧拉角
    double sy = sqrt(rotation_matrix.at<double>(0,0) * rotation_matrix.at<double>(0,0) +
                     rotation_matrix.at<double>(1,0) * rotation_matrix.at<double>(1,0));
    bool singular = sy < 1e-6;
    double roll, pitch, yaw;
    if (!singular) {
        roll = atan2(rotation_matrix.at<double>(2,1), rotation_matrix.at<double>(2,2));
        pitch = atan2(-rotation_matrix.at<double>(2,0), sy);
        yaw = atan2(rotation_matrix.at<double>(1,0), rotation_matrix.at<double>(0,0));
    } else {
        roll = atan2(-rotation_matrix.at<double>(1,2), rotation_matrix.at<double>(1,1));
        pitch = atan2(-rotation_matrix.at<double>(2,0), sy);
        yaw = 0;
    }

    // 转换为度数
    roll = roll * 180.0 / CV_PI;
    pitch = pitch * 180.0 / CV_PI;
    yaw = yaw * 180.0 / CV_PI;

    std::cout << "  旋转角度 (度):" << std::endl;
    std::cout << "    Roll (绕X轴): " << std::fixed << std::setprecision(1) << roll << "°" << std::endl;
    std::cout << "    Pitch (绕Y轴): " << std::fixed << std::setprecision(1) << pitch << "°" << std::endl;
    std::cout << "    Yaw (绕Z轴): " << std::fixed << std::setprecision(1) << yaw << "°" << std::endl;

    // 5. 综合精度评估
    std::cout << "\n综合精度评估:" << std::endl;
    if (mean_error < 0.5) {
        std::cout << "  等级: 优秀 (平均误差 < 0.5 像素)" << std::endl;
    } else if (mean_error < 1.0) {
        std::cout << "  等级: 良好 (平均误差 < 1.0 像素)" << std::endl;
    } else if (mean_error < 2.0) {
        std::cout << "  等级: 一般 (平均误差 < 2.0 像素)" << std::endl;
    } else if (mean_error < 5.0) {
        std::cout << "  等级: 较差 (平均误差 < 5.0 像素)" << std::endl;
    } else {
        std::cout << "  等级: 很差 (平均误差 >= 5.0 像素)" << std::endl;
    }

    // 6. 在图像上可视化结果
    for (size_t i = 0; i < image_points.size() && i < projected_points.size(); i++) {
        // 绿色圆圈：原始检测点
        cv::circle(image, image_points[i], 6, cv::Scalar(0, 255, 0), 2);
        // 蓝色圆圈：重投影点
        cv::circle(image, projected_points[i], 4, cv::Scalar(255, 0, 0), 2);
        // 红色线段：连接检测点和投影点
        cv::line(image, image_points[i], projected_points[i], cv::Scalar(0, 0, 255), 1);

        // 添加点的标号
        cv::putText(image, std::to_string(i),
                   cv::Point(image_points[i].x + 10, image_points[i].y - 10),
                   cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(255, 255, 255), 1);
    }

    // 添加误差信息到图像上
    std::string error_text = "Mean Error: " + std::to_string(mean_error).substr(0, 4) + " px";
    cv::putText(image, error_text, cv::Point(10, 30),
               cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 255, 255), 2);

    std::string distance_text = "Distance: " + std::to_string(distance).substr(0, 5) + " m";
    cv::putText(image, distance_text, cv::Point(10, 60),
               cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 255, 255), 2);
}

// 高级验证方法：交叉验证
void crossValidation(const std::vector<cv::Point3f>& object_points,
                    const std::vector<cv::Point2f>& image_points,
                    const cv::Mat& camera_matrix,
                    const cv::Mat& dist_coeffs) {

    std::cout << "\n==================== 交叉验证 ====================" << std::endl;

    if (object_points.size() < 4) {
        std::cout << "点数不足，无法进行交叉验证" << std::endl;
        return;
    }

    std::vector<double> errors;

    // 留一法交叉验证
    for (size_t i = 0; i < object_points.size(); i++) {
        // 创建训练集（去掉第i个点）
        std::vector<cv::Point3f> train_obj_pts;
        std::vector<cv::Point2f> train_img_pts;

        for (size_t j = 0; j < object_points.size(); j++) {
            if (j != i) {
                train_obj_pts.push_back(object_points[j]);
                train_img_pts.push_back(image_points[j]);
            }
        }

        // 用训练集求解位姿
        cv::Mat rvec, tvec;
        bool success = cv::solvePnP(train_obj_pts, train_img_pts, camera_matrix, dist_coeffs, rvec, tvec);

        if (success) {
            // 预测被留出的点
            std::vector<cv::Point3f> test_obj_pt = {object_points[i]};
            std::vector<cv::Point2f> predicted_pt;
            cv::projectPoints(test_obj_pt, rvec, tvec, camera_matrix, dist_coeffs, predicted_pt);

            // 计算预测误差
            double error = cv::norm(image_points[i] - predicted_pt[0]);
            errors.push_back(error);

            std::cout << "留出点 " << i << ": 预测误差 " << std::fixed << std::setprecision(3)
                      << error << " 像素" << std::endl;
        }
    }

    if (!errors.empty()) {
        double mean_cv_error = std::accumulate(errors.begin(), errors.end(), 0.0) / errors.size();
        std::cout << "交叉验证平均误差: " << std::fixed << std::setprecision(3)
                  << mean_cv_error << " 像素" << std::endl;
    }
}

// RANSAC验证：检测异常点
void ransacValidation(const std::vector<cv::Point3f>& object_points,
                     const std::vector<cv::Point2f>& image_points,
                     const cv::Mat& camera_matrix,
                     const cv::Mat& dist_coeffs) {

    std::cout << "\n==================== RANSAC验证 ====================" << std::endl;

    cv::Mat rvec, tvec, inliers;
    bool success = cv::solvePnPRansac(object_points, image_points, camera_matrix, dist_coeffs,
                                     rvec, tvec, false, 100, 2.0, 0.99, inliers);

    if (success) {
        std::cout << "RANSAC成功，内点数量: " << inliers.rows << "/" << object_points.size() << std::endl;

        // 显示哪些点是内点
        std::vector<bool> is_inlier(object_points.size(), false);
        for (int i = 0; i < inliers.rows; i++) {
            int idx = inliers.at<int>(i);
            is_inlier[idx] = true;
        }

        for (size_t i = 0; i < object_points.size(); i++) {
            std::cout << "点 " << i << ": " << (is_inlier[i] ? "内点" : "异常点") << std::endl;
        }

        // 计算RANSAC结果的重投影误差
        std::vector<cv::Point2f> projected_points;
        cv::projectPoints(object_points, rvec, tvec, camera_matrix, dist_coeffs, projected_points);

        double total_error = 0.0;
        for (size_t i = 0; i < image_points.size(); i++) {
            if (is_inlier[i]) {
                double error = cv::norm(image_points[i] - projected_points[i]);
                total_error += error;
            }
        }

        double mean_error = total_error / inliers.rows;
        std::cout << "RANSAC内点平均误差: " << std::fixed << std::setprecision(3)
                  << mean_error << " 像素" << std::endl;
    } else {
        std::cout << "RANSAC失败" << std::endl;
    }
}

// 多种算法对比验证
void compareAlgorithms(const std::vector<cv::Point3f>& object_points,
                      const std::vector<cv::Point2f>& image_points,
                      const cv::Mat& camera_matrix,
                      const cv::Mat& dist_coeffs) {

    std::cout << "\n==================== 算法对比验证 ====================" << std::endl;

    std::vector<int> methods = {cv::SOLVEPNP_ITERATIVE, cv::SOLVEPNP_EPNP,
                               cv::SOLVEPNP_P3P, cv::SOLVEPNP_DLS};
    std::vector<std::string> method_names = {"ITERATIVE", "EPnP", "P3P", "DLS"};

    for (size_t i = 0; i < methods.size(); i++) {
        cv::Mat rvec, tvec;
        bool success = false;

        try {
            success = cv::solvePnP(object_points, image_points, camera_matrix, dist_coeffs,
                                  rvec, tvec, false, methods[i]);
        } catch (const cv::Exception& e) {
            std::cout << method_names[i] << ": 失败 - " << e.what() << std::endl;
            continue;
        }

        if (success) {
            // 计算重投影误差
            std::vector<cv::Point2f> projected_points;
            cv::projectPoints(object_points, rvec, tvec, camera_matrix, dist_coeffs, projected_points);

            double total_error = 0.0;
            for (size_t j = 0; j < image_points.size(); j++) {
                total_error += cv::norm(image_points[j] - projected_points[j]);
            }
            double mean_error = total_error / image_points.size();

            double distance = cv::norm(tvec);

            std::cout << method_names[i] << ": 平均误差 " << std::fixed << std::setprecision(3)
                      << mean_error << " 像素, 距离 " << std::setprecision(3)
                      << distance << " 米" << std::endl;
        } else {
            std::cout << method_names[i] << ": 求解失败" << std::endl;
        }
    }
}

int main(int argc, char *argv[]){
    cv::Mat image = cv::imread("/home/<USER>/work/charge_robot/test.png", cv::IMREAD_COLOR);
    if(image.empty()){
        std::cout << "Could not open or find the image" << std::endl;
        return -1;
    }

    std::cout << "image.cols: " << image.cols << " image.rows: " << image.rows << std::endl;
    std::cout << "center_x: " << image.cols / 2 << " center_y: " << image.rows / 2 << std::endl;

    // cv::circle(image, cv::Point(image.cols / 2, image.rows / 2), 5, cv::Scalar(0, 255, 0), 2);
    // (cols, rows)==>(596, 427)
    cv::Point pt1(image.cols / 2 - 140, image.rows / 2 - 65 );
    cv::Point pt2(image.cols / 2 + 60, image.rows / 2 + 125 );
    // 确保坐标在图像范围内（防止越界）
    pt1.x = std::max(0, pt1.x);
    pt1.y = std::max(0, pt1.y);
    pt2.x = std::min(image.cols - 1, pt2.x);
    pt2.y = std::min(image.rows - 1, pt2.y);
    // 提取矩形区域（ROI）
    // cv::Mat roi = image(cv::Rect(pt1, pt2));
    cv::Mat roi = image(cv::Rect(pt1, pt2)).clone(); // 这是复制

    cv::rectangle(image, pt1, pt2, cv::Scalar(0, 0, 255), 2);

    // 保存 ROI 到本地文件
    // std::string output_path = "/home/<USER>/work/充电项目/qyQ07_roi.png";  // 保存路径
    // bool success = cv::imwrite(output_path, roi);
    // if (success) {
    //     std::cout << "ROI 已保存至: " << output_path << std::endl;
    // } else {
    //     std::cerr << "保存失败！请检查路径或权限。" << std::endl;
    // }
    cv::Mat gray_roi;
    cv::cvtColor(roi, gray_roi, cv::COLOR_BGR2GRAY);
    // 高斯模糊 去除噪声
    cv::GaussianBlur(gray_roi, gray_roi, cv::Size(5, 5), 0);
    // cv::imshow("gray_roi", gray_roi);
    // cv::waitKey(0);
    // 显示边缘检测结果
    cv::Mat edges;
    cv::Canny(gray_roi, edges, 10, 100);
    cv::imshow("Edges", edges);
    cv::waitKey(1);

    // =======================================================查找轮廓=======================================================
    // std::vector<std::vector<cv::Point>> contours;
    // cv::findContours(edges, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
    // std::cout << "contours.size(): " << contours.size() << std::endl;
    // // 筛选出圆形轮廓
    // std::vector<std::vector<cv::Point>> circularContours;
    // for (const auto& contour : contours) {
    //     double area = cv::contourArea(contour);
    //     double perimeter = cv::arcLength(contour, true);
    //     if (perimeter == 0) continue;

    //     double circularity = 4 * CV_PI * area / (perimeter * perimeter);
    //     std::cout << "circularity: " << circularity << std::endl;
    //     if (circularity > 0.01) {  // 越接近1越圆
    //         circularContours.push_back(contour);
    //     }
    // }
    // cv::drawContours(image, circularContours, -1, cv::Scalar(0, 255, 0), 2);
    // cv::imshow("image", image);
    // cv::waitKey(1);

    // 霍夫圆变换检测圆
    std::vector<cv::Vec3f> circles;
    cv::HoughCircles(gray_roi, circles, cv::HOUGH_GRADIENT, 1, 30, 100, 30, 20, 30);
    // cv::HoughCircles(
    //     gray_roi,           // 输入图像（灰度图）
    //     circles,            // 输出检测到的圆的信息
    //     cv::HOUGH_GRADIENT, // 检测方法
    //     1,                  // 累加器分辨率
    //     10,                 // 最小圆心距离
    //     100,                // Canny边缘检测的高阈值
    //     50,                 // 累加器阈值
    //     10,                 // 最小圆半径
    //     100                 // 最大圆半径
    // );
    // 在原图上画出检测到的圆
    for(size_t i = 0; i < circles.size(); i++){
        cv::Point center(cvRound(circles[i][0]), cvRound(circles[i][1]));
        int radius = cvRound(circles[i][2]);
        // 画出圆心
        cv::circle(roi, center, 2, cv::Scalar(0, 255, 0), 2);
        // 画出圆
        cv::circle(roi, center, radius, cv::Scalar(0, 0, 255), 2);
        std::cout << "circle " << i << " center = ( " << center.x << ", " << center.y << " ), radius = " << radius << std::endl;
    }
    cv::imshow("roi", roi);
    // 定义3D点，同一平面，z=0
    std::vector<cv::Point3f> object_points;
    object_points.push_back(cv::Point3f(0.016, 0, 0));
    object_points.push_back(cv::Point3f(0, 0, 0)); // 圆心
    object_points.push_back(cv::Point3f(-0.016, 0, 0));
    object_points.push_back(cv::Point3f(0.008, 0.0139, 0));
    object_points.push_back(cv::Point3f(-0.008, 0.0139, 0));
    // 定义图像中的目标点顺序
    std::vector<cv::Point2f> image_points = image_points_sort(circles, pt1);
      // 相机内参
    cv::Mat camera_matrix = (cv::Mat_<double>(3, 3) << 528.6953213954334, 0, 621.6598968327049, 0, 469.7443489102585, 344.7801912987854, 0, 0, 1);
    cv::Mat dist_coeffs = (cv::Mat_<double>(5, 1) << 0, 0, 0, 0, 0);
    cv::Mat rvec, tvec;
    cv::solvePnP(object_points, image_points, camera_matrix, dist_coeffs, rvec, tvec);

    // 基础验证
    validatePnPResult(object_points, image_points, camera_matrix, dist_coeffs, rvec, tvec, image);

    // 高级验证方法
    // crossValidation(object_points, image_points, camera_matrix, dist_coeffs);
    // ransacValidation(object_points, image_points, camera_matrix, dist_coeffs);
    // compareAlgorithms(object_points, image_points, camera_matrix, dist_coeffs);

    // 显示结果
    cv::imshow("solvePnP验证结果", image);

    cv::waitKey(0);
    
}